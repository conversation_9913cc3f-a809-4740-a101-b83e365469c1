#!/usr/bin/env python3
"""
EverQuest Overlay Application
Integrates log monitoring, trigger system, and transparent overlay
to provide real-time game state information.
"""

import sys
import os
import argparse
import threading
import time
from pathlib import Path

from eq_log_monitor import EQLogMonitor
from trigger_manager import <PERSON><PERSON><PERSON>anager
from example_triggers import create_auto_attack_trigger, create_auto_attack_off_trigger
from transparent_overlay import TransparentOverlay


class EQOverlayApp:
    """
    Main application that coordinates log monitoring, triggers, and overlay display.
    """
    
    def __init__(self, log_file_path: str, config_file: str = None):
        """
        Initialize the EQ overlay application.
        
        Args:
            log_file_path: Path to the EverQuest log file to monitor
            config_file: Optional path to trigger configuration file
        """
        self.log_file_path = log_file_path
        self.config_file = config_file
        
        # Initialize components
        self.log_monitor = EQLogMonitor(log_file_path)
        self.trigger_manager = TriggerManager(config_file)
        self.overlay = None
        
        # Threading
        self.running = False
        self.monitor_thread = None
        
    def setup_default_triggers(self):
        """Set up default triggers if no config file is provided."""
        if not self.config_file or not Path(self.config_file).exists():
            print("Setting up default triggers...")
            
            # Add auto attack triggers
            self.trigger_manager.add_trigger(create_auto_attack_trigger())
            self.trigger_manager.add_trigger(create_auto_attack_off_trigger())
            
            # Add some basic combat triggers
            from log_trigger import LogTrigger
            
            combat_start = LogTrigger(
                name="combat_start",
                phrases=["You attack", "You hit", "You slash", "You pierce", "You crush"],
                state_variable="in_combat",
                update_function="set_true",
                enabled=True,
                case_sensitive=False
            )
            self.trigger_manager.add_trigger(combat_start)
            
            # Combat end trigger (simplified - in reality this would be more complex)
            combat_end = LogTrigger(
                name="combat_end",
                phrases=["You sit down", "You begin to meditate"],
                state_variable="in_combat",
                update_function="set_false",
                enabled=True,
                case_sensitive=False
            )
            self.trigger_manager.add_trigger(combat_end)
            
            # Meditation triggers
            med_start = LogTrigger(
                name="meditation_start",
                phrases=["You sit down", "You begin to meditate"],
                state_variable="meditating",
                update_function="set_true",
                enabled=True,
                case_sensitive=False
            )
            self.trigger_manager.add_trigger(med_start)
            
            med_stop = LogTrigger(
                name="meditation_stop",
                phrases=["You stand up", "You stop meditating"],
                state_variable="meditating",
                update_function="set_false",
                enabled=True,
                case_sensitive=False
            )
            self.trigger_manager.add_trigger(med_stop)
            
            # Death trigger
            death = LogTrigger(
                name="death",
                phrases=["You have been slain", "You died"],
                state_variable="is_dead",
                update_function="set_true",
                enabled=True,
                case_sensitive=False
            )
            self.trigger_manager.add_trigger(death)
            
            # Resurrection trigger
            resurrect = LogTrigger(
                name="resurrection",
                phrases=["You have been resurrected", "You are no longer dead"],
                state_variable="is_dead",
                update_function="set_false",
                enabled=True,
                case_sensitive=False
            )
            self.trigger_manager.add_trigger(resurrect)
            
            print("Default triggers configured.")
    
    def start_monitoring(self):
        """Start the log monitoring in a separate thread."""
        if not self.log_monitor.validate_log_file():
            print("Invalid log file. Cannot start monitoring.")
            return False
        
        self.running = True
        
        def monitor_loop():
            print(f"Starting log monitoring: {self.log_file_path}")
            
            # Set initial position to end of file to only monitor new content
            self.log_monitor.last_position = self.log_monitor.get_file_size()
            
            while self.running:
                try:
                    new_content = self.log_monitor.read_new_content()
                    if new_content:
                        lines = new_content.strip().split('\n')
                        for line in lines:
                            if line.strip():  # Skip empty lines
                                activated = self.trigger_manager.process_log_line(line)
                                if activated:
                                    print(f"Line: {line.strip()}")
                                    print(f"Activated triggers: {activated}")
                    
                    time.sleep(0.1)  # Small delay to prevent excessive CPU usage
                    
                except Exception as e:
                    print(f"Error in monitoring loop: {e}")
                    time.sleep(1)  # Longer delay on error
        
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
        return True
    
    def stop_monitoring(self):
        """Stop the log monitoring."""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        print("Log monitoring stopped.")
    
    def start_overlay(self):
        """Start the transparent overlay."""
        try:
            self.overlay = TransparentOverlay(self.trigger_manager)
            self.overlay.run()
        except Exception as e:
            print(f"Error starting overlay: {e}")
    
    def run(self):
        """Run the complete application."""
        print("EverQuest Overlay Application")
        print("=" * 40)
        
        # Setup triggers
        self.setup_default_triggers()
        
        # Show configured triggers
        print("\nConfigured triggers:")
        self.trigger_manager.list_triggers()
        
        # Start monitoring
        if not self.start_monitoring():
            return
        
        print(f"\nMonitoring started. Current states:")
        for name, value in self.trigger_manager.get_all_states().items():
            print(f"  {name}: {value}")
        
        print("\nStarting overlay...")
        print("Press Escape in overlay window or Ctrl+C here to exit.")
        
        try:
            # Start the overlay (this will block until overlay is closed)
            self.start_overlay()
        except KeyboardInterrupt:
            print("\nShutting down...")
        finally:
            self.stop_monitoring()
            
            # Save configuration if we created default triggers
            if not self.config_file:
                default_config = "eq_overlay_config.json"
                self.trigger_manager.save_config(default_config)
                print(f"Configuration saved to {default_config}")


def find_eq_log_file(eq_directory: str = None) -> str:
    """
    Find the most recent EverQuest log file.
    
    Args:
        eq_directory: Optional EQ installation directory
        
    Returns:
        Path to the most recent log file
    """
    if eq_directory:
        monitor = EQLogMonitor("")
        latest_log = monitor.find_latest_log(eq_directory)
        if latest_log:
            return str(latest_log)
    
    # If no directory specified or no log found, return None
    return None


def main():
    parser = argparse.ArgumentParser(
        description="EverQuest Overlay Application - Real-time game state monitoring",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Use specific log file
  python eq_overlay_app.py -f "C:\\TAKPv22\\Logs\\eqlog_Playername_server.txt"
  
  # Auto-find latest log in EQ directory
  python eq_overlay_app.py -d "C:\\TAKPv22"
  
  # Use custom trigger configuration
  python eq_overlay_app.py -f "logfile.txt" -c "my_triggers.json"
        """
    )
    
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument(
        '-f', '--file',
        help='Path to specific EverQuest log file'
    )
    group.add_argument(
        '-d', '--directory',
        help='Path to EverQuest installation directory (will find latest log)'
    )
    
    parser.add_argument(
        '-c', '--config',
        help='Path to trigger configuration file (JSON)'
    )
    
    args = parser.parse_args()
    
    # Determine log file path
    if args.file:
        log_file = args.file
    else:
        log_file = find_eq_log_file(args.directory)
        if not log_file:
            print(f"No log file found in directory: {args.directory}")
            sys.exit(1)
        print(f"Found log file: {log_file}")
    
    # Validate log file exists
    if not Path(log_file).exists():
        print(f"Log file not found: {log_file}")
        sys.exit(1)
    
    # Create and run the application
    app = EQOverlayApp(log_file, args.config)
    app.run()


if __name__ == "__main__":
    main()
