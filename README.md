# SophieQ

EverQuest log monitoring and overlay system with configurable triggers and real-time state display.

## Features

- **Real-time log monitoring**: Watches EverQuest log files for new content
- **Configurable triggers**: Define custom phrases that update game state variables
- **Transparent overlay**: Shows current game state on screen with auto-updating display
- **Serializable configuration**: Save and load trigger configurations as JSON
- **State management**: Centralized state tracking with callback support
- **Auto attack detection**: Built-in example trigger for auto attack status

## Components

### 1. Log Trigger System (`log_trigger.py`)
- `LogTrigger`: Configurable trigger that watches for specific phrases
- `StateManager`: Manages state variables and callbacks
- Serializable to/from JSON

### 2. Trigger Manager (`trigger_manager.py`)
- `TriggerManager`: Coordinates multiple triggers and state management
- Handles trigger lifecycle and configuration persistence
- Provides monitoring integration

### 3. Transparent Overlay (`transparent_overlay.py`)
- Windows 11 compatible transparent overlay
- Real-time display of game state information
- Auto-updates based on trigger state changes
- Displays auto attack, combat, meditation, and death status

### 4. Example Triggers (`example_triggers.py`)
- Pre-configured triggers for common EverQuest events
- Auto attack on/off detection
- Combat, meditation, death, and spell casting triggers

### 5. Integrated Application (`eq_overlay_app.py`)
- Complete application combining all components
- Command-line interface for easy setup
- Auto-discovery of EverQuest log files

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Run the Complete Application
```bash
# Auto-find latest log file in EQ directory
python eq_overlay_app.py -d "C:\TAKPv22"

# Use specific log file
python eq_overlay_app.py -f "C:\TAKPv22\Logs\eqlog_Playername_server.txt"

# Use custom trigger configuration
python eq_overlay_app.py -f "logfile.txt" -c "my_triggers.json"
```

### 3. Test the Auto Attack Trigger
```bash
# Run automated tests
python test_auto_attack.py

# Run interactive test
python test_auto_attack.py --interactive
```

## Usage Examples

### Creating Custom Triggers

```python
from log_trigger import LogTrigger
from trigger_manager import TriggerManager

# Create a trigger for spell casting
spell_trigger = LogTrigger(
    name="spell_casting",
    phrases=["You begin casting", "You start casting"],
    state_variable="casting_spell",
    update_function="set_true",
    enabled=True,
    case_sensitive=False
)

# Add to manager
manager = TriggerManager()
manager.add_trigger(spell_trigger)
```

### Available Update Functions
- `set_true`: Set state variable to True
- `set_false`: Set state variable to False
- `toggle`: Toggle boolean state variable
- `increment`: Increment numeric state variable
- `timestamp`: Set to current Unix timestamp
- `datetime_string`: Set to current ISO datetime string

### State Callbacks
```python
def on_auto_attack_change(var_name, old_value, new_value):
    print(f"Auto attack changed: {old_value} -> {new_value}")

manager.register_state_callback("auto_attack", on_auto_attack_change)
```

## Configuration File Format

Triggers and states can be saved/loaded as JSON:

```json
{
  "triggers": {
    "auto_attack_on": {
      "name": "auto_attack_on",
      "phrases": ["Auto attack is on"],
      "state_variable": "auto_attack",
      "update_function": "set_true",
      "enabled": true,
      "case_sensitive": false,
      "last_triggered": null,
      "trigger_count": 0
    }
  },
  "states": {
    "states": {
      "auto_attack": false
    }
  }
}
```

## Overlay Display

The transparent overlay shows:
- ⚔️ Auto Attack status (ON/OFF)
- 🗡️ Combat status (ACTIVE/Peaceful)
- 🧘 Meditation status
- ✨ Spell casting status
- 💀 Death status

Colors change based on state:
- Red: Dead
- Orange: In combat
- Yellow: Auto attack on
- Light green: Peaceful

## Files

- `log_trigger.py` - Core trigger and state management classes
- `trigger_manager.py` - Trigger coordination and management
- `example_triggers.py` - Pre-configured example triggers
- `transparent_overlay.py` - Windows overlay display
- `eq_overlay_app.py` - Complete integrated application
- `test_auto_attack.py` - Test scripts for trigger functionality
- `eq_log_monitor.py` - Original log monitoring functionality

## Requirements

- Windows 11 (for overlay functionality)
- Python 3.7+
- tkinter (usually included with Python)
- pywin32 (for Windows API access)

See `requirements.txt` for complete dependency list.
