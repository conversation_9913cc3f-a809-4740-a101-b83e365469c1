#!/usr/bin/env python3
"""
Log Trigger System for EverQuest Log Monitoring
Provides configurable triggers that watch for specific phrases in log files
and update state variables when triggered.
"""

import json
import time
from datetime import datetime
from typing import Dict, Any, Callable, List, Optional
from dataclasses import dataclass, asdict, field


@dataclass
class LogTrigger:
    """
    A configurable trigger that watches for specific phrases in log files.
    
    Attributes:
        name: Unique identifier for this trigger
        phrases: List of phrases that can activate this trigger
        state_variable: Name of the state variable to update when triggered
        update_function: Function to call when trigger activates
        enabled: Whether this trigger is currently active
        case_sensitive: Whether phrase matching is case sensitive
        last_triggered: Timestamp of last trigger activation
        trigger_count: Number of times this trigger has been activated
    """
    name: str
    phrases: List[str]
    state_variable: str
    update_function: str = "set_true"  # Name of function to call
    enabled: bool = True
    case_sensitive: bool = False
    last_triggered: Optional[float] = None
    trigger_count: int = 0
    
    # Non-serialized fields
    _update_func: Optional[Callable] = field(default=None, init=False, repr=False)
    
    def __post_init__(self):
        """Initialize the update function after creation."""
        self._update_func = self._get_update_function()
    
    def _get_update_function(self) -> Callable:
        """Get the actual function object for the update_function name."""
        update_functions = {
            "set_true": lambda state_manager, var_name: state_manager.set_state(var_name, True),
            "set_false": lambda state_manager, var_name: state_manager.set_state(var_name, False),
            "toggle": lambda state_manager, var_name: state_manager.toggle_state(var_name),
            "increment": lambda state_manager, var_name: state_manager.increment_state(var_name),
            "timestamp": lambda state_manager, var_name: state_manager.set_state(var_name, time.time()),
            "datetime_string": lambda state_manager, var_name: state_manager.set_state(var_name, datetime.now().isoformat()),
        }
        return update_functions.get(self.update_function, update_functions["set_true"])
    
    def check_line(self, line: str) -> bool:
        """
        Check if a log line contains any of the trigger phrases.
        
        Args:
            line: The log line to check
            
        Returns:
            True if the line contains a trigger phrase, False otherwise
        """
        if not self.enabled:
            return False
            
        check_line = line if self.case_sensitive else line.lower()
        
        for phrase in self.phrases:
            check_phrase = phrase if self.case_sensitive else phrase.lower()
            if check_phrase in check_line:
                return True
        
        return False
    
    def activate(self, state_manager, line: str = "") -> bool:
        """
        Activate this trigger, updating the state variable.
        
        Args:
            state_manager: The StateManager instance to update
            line: The log line that triggered this (for logging purposes)
            
        Returns:
            True if the trigger was successfully activated
        """
        if not self.enabled:
            return False
            
        try:
            # Update the state using the configured function
            if self._update_func:
                self._update_func(state_manager, self.state_variable)
            
            # Update trigger metadata
            self.last_triggered = time.time()
            self.trigger_count += 1
            
            print(f"Trigger '{self.name}' activated: {self.state_variable} = {state_manager.get_state(self.state_variable)}")
            return True
            
        except Exception as e:
            print(f"Error activating trigger '{self.name}': {e}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert trigger to dictionary for serialization."""
        data = asdict(self)
        # Remove non-serializable fields
        data.pop('_update_func', None)
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LogTrigger':
        """Create trigger from dictionary."""
        # Remove any fields that aren't in the dataclass
        valid_fields = {f.name for f in cls.__dataclass_fields__.values()}
        filtered_data = {k: v for k, v in data.items() if k in valid_fields}
        return cls(**filtered_data)
    
    def to_json(self) -> str:
        """Convert trigger to JSON string."""
        return json.dumps(self.to_dict(), indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'LogTrigger':
        """Create trigger from JSON string."""
        data = json.loads(json_str)
        return cls.from_dict(data)


class StateManager:
    """
    Manages state variables that can be updated by triggers.
    """
    
    def __init__(self):
        self._states: Dict[str, Any] = {}
        self._callbacks: Dict[str, List[Callable]] = {}
    
    def set_state(self, variable_name: str, value: Any) -> None:
        """Set a state variable to a specific value."""
        old_value = self._states.get(variable_name)
        self._states[variable_name] = value
        
        # Call any registered callbacks for this variable
        if variable_name in self._callbacks:
            for callback in self._callbacks[variable_name]:
                try:
                    callback(variable_name, old_value, value)
                except Exception as e:
                    print(f"Error in state callback for {variable_name}: {e}")
    
    def get_state(self, variable_name: str, default: Any = None) -> Any:
        """Get the current value of a state variable."""
        return self._states.get(variable_name, default)
    
    def toggle_state(self, variable_name: str) -> None:
        """Toggle a boolean state variable."""
        current = self.get_state(variable_name, False)
        self.set_state(variable_name, not current)
    
    def increment_state(self, variable_name: str, amount: int = 1) -> None:
        """Increment a numeric state variable."""
        current = self.get_state(variable_name, 0)
        self.set_state(variable_name, current + amount)
    
    def register_callback(self, variable_name: str, callback: Callable) -> None:
        """Register a callback to be called when a state variable changes."""
        if variable_name not in self._callbacks:
            self._callbacks[variable_name] = []
        self._callbacks[variable_name].append(callback)
    
    def get_all_states(self) -> Dict[str, Any]:
        """Get a copy of all current state variables."""
        return self._states.copy()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert state manager to dictionary for serialization."""
        return {
            'states': self._states.copy()
        }
    
    def from_dict(self, data: Dict[str, Any]) -> None:
        """Load state manager from dictionary."""
        if 'states' in data:
            self._states = data['states'].copy()
    
    def to_json(self) -> str:
        """Convert state manager to JSON string."""
        return json.dumps(self.to_dict(), indent=2)
    
    def from_json(self, json_str: str) -> None:
        """Load state manager from JSON string."""
        data = json.loads(json_str)
        self.from_dict(data)
