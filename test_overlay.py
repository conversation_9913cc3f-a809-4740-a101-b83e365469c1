#!/usr/bin/env python3
"""
Test script to demonstrate the overlay with trigger manager integration.
This shows how to properly connect the overlay to a working trigger system.
"""

import threading
import time
from trigger_manager import Trigger<PERSON>anager
from example_triggers import create_auto_attack_trigger, create_auto_attack_off_trigger
from transparent_overlay import TransparentOverlay


def simulate_log_events(trigger_manager):
    """Simulate log events to demonstrate the trigger system."""
    print("Starting log event simulation...")
    
    # Wait a bit for overlay to start
    time.sleep(2)
    
    events = [
        ("Auto attack is on", 3),
        ("You attack a goblin", 2),
        ("Auto attack is off", 3),
        ("You sit down", 2),
        ("You begin to meditate", 4),
        ("You stand up", 2),
        ("Auto attack is on", 3),
        ("You have been slain by a dragon", 5),
    ]
    
    for event, delay in events:
        print(f"Simulating: {event}")
        activated = trigger_manager.process_log_line(f"[Mon Dec 25 10:00:00 2023] {event}")
        if activated:
            print(f"  -> Activated triggers: {activated}")
        
        # Show current states
        states = trigger_manager.get_all_states()
        print(f"  -> Current states: {states}")
        
        time.sleep(delay)


def main():
    """Run the overlay test with simulated events."""
    print("Overlay Test with Trigger Manager")
    print("=" * 40)
    
    # Create trigger manager and add triggers
    trigger_manager = TriggerManager()
    trigger_manager.add_trigger(create_auto_attack_trigger())
    trigger_manager.add_trigger(create_auto_attack_off_trigger())
    
    # Add some additional triggers for demonstration
    from log_trigger import LogTrigger
    
    combat_trigger = LogTrigger(
        name="combat_start",
        phrases=["You attack", "You hit"],
        state_variable="in_combat",
        update_function="set_true",
        enabled=True,
        case_sensitive=False
    )
    trigger_manager.add_trigger(combat_trigger)
    
    meditation_trigger = LogTrigger(
        name="meditation",
        phrases=["You sit down", "You begin to meditate"],
        state_variable="meditating",
        update_function="set_true",
        enabled=True,
        case_sensitive=False
    )
    trigger_manager.add_trigger(meditation_trigger)
    
    meditation_stop_trigger = LogTrigger(
        name="meditation_stop",
        phrases=["You stand up"],
        state_variable="meditating",
        update_function="set_false",
        enabled=True,
        case_sensitive=False
    )
    trigger_manager.add_trigger(meditation_stop_trigger)
    
    death_trigger = LogTrigger(
        name="death",
        phrases=["You have been slain", "You died"],
        state_variable="is_dead",
        update_function="set_true",
        enabled=True,
        case_sensitive=False
    )
    trigger_manager.add_trigger(death_trigger)
    
    print("Triggers configured:")
    trigger_manager.list_triggers()
    
    print("\nStarting overlay with trigger manager...")
    print("Watch the overlay display change as events are simulated!")
    print("Press Escape in the overlay window to exit.")
    
    # Start simulation in a separate thread
    simulation_thread = threading.Thread(
        target=simulate_log_events, 
        args=(trigger_manager,), 
        daemon=True
    )
    simulation_thread.start()
    
    # Start the overlay (this will block until overlay is closed)
    try:
        overlay = TransparentOverlay(trigger_manager)
        overlay.run()
    except Exception as e:
        print(f"Error running overlay: {e}")
    
    print("Test completed!")


if __name__ == "__main__":
    main()
