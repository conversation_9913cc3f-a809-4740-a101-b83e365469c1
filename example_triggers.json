{"triggers": {"auto_attack_on": {"name": "auto_attack_on", "phrases": ["Auto attack is on", "You attack", "You hit", "You slash"], "state_variable": "auto_attack", "update_function": "set_true", "enabled": true, "case_sensitive": false, "last_triggered": null, "trigger_count": 0}, "auto_attack_off": {"name": "auto_attack_off", "phrases": ["Auto attack is off"], "state_variable": "auto_attack", "update_function": "set_false", "enabled": true, "case_sensitive": false, "last_triggered": null, "trigger_count": 0}, "combat_start": {"name": "combat_start", "phrases": ["You begin casting", "You attack", "You hit", "You slash"], "state_variable": "in_combat", "update_function": "set_true", "enabled": true, "case_sensitive": false, "last_triggered": null, "trigger_count": 0}, "player_death": {"name": "player_death", "phrases": ["You have been slain", "You died"], "state_variable": "is_dead", "update_function": "set_true", "enabled": true, "case_sensitive": false, "last_triggered": null, "trigger_count": 0}, "zone_change": {"name": "zone_change", "phrases": ["You have entered"], "state_variable": "zone_change_time", "update_function": "timestamp", "enabled": true, "case_sensitive": false, "last_triggered": null, "trigger_count": 0}, "level_up": {"name": "level_up", "phrases": ["You have gained a level", "Welcome to level"], "state_variable": "level_up_count", "update_function": "increment", "enabled": true, "case_sensitive": false, "last_triggered": null, "trigger_count": 0}, "spell_casting": {"name": "spell_casting", "phrases": ["You begin casting", "You start casting"], "state_variable": "casting_spell", "update_function": "set_true", "enabled": true, "case_sensitive": false, "last_triggered": null, "trigger_count": 0}, "spell_complete": {"name": "spell_complete", "phrases": ["Your spell is complete", "You have finished casting"], "state_variable": "casting_spell", "update_function": "set_false", "enabled": true, "case_sensitive": false, "last_triggered": null, "trigger_count": 0}, "invalid_target": {"name": "invalid_target", "phrases": ["Your target is too far away", "You"], "state_variable": "invalid_target", "update_function": "set_true", "enabled": true, "case_sensitive": false, "last_triggered": null, "trigger_count": 0}, "meditation": {"name": "meditation", "phrases": ["You sit down", "You begin to meditate"], "state_variable": "meditating", "update_function": "set_true", "enabled": true, "case_sensitive": false, "last_triggered": null, "trigger_count": 0}, "meditation_stop": {"name": "meditation_stop", "phrases": ["You stand up", "You stop meditating"], "state_variable": "meditating", "update_function": "set_false", "enabled": true, "case_sensitive": false, "last_triggered": null, "trigger_count": 0}}, "states": {"states": {"auto_attack": false, "in_combat": false, "is_dead": false, "zone_change_time": false, "level_up_count": false, "casting_spell": false, "meditating": false, "invalid_target": false}}}