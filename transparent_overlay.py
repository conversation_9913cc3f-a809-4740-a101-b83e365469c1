#!/usr/bin/env python3
"""
Transparent Overlay for Windows 11
Creates a transparent overlay window that displays EverQuest game state information.
Integrates with the trigger system to show real-time game status.
"""

import tkinter as tk
import sys
import os
import win32gui
import win32con
import win32api
import threading
import time
from typing import Optional
from trigger_manager import TriggerManager

class TransparentOverlay:
    def __init__(self, trigger_manager: Optional[TriggerManager] = None):
        self.root = tk.Tk()
        self.running = True
        self.trigger_manager = trigger_manager
        self.status_label = None
        self.update_interval = 100  # milliseconds
        self.setup_window()
        self.create_content()
        self.bind_keys()
        self.start_global_hotkey()
        self.start_status_updates()
        
    def start_global_hotkey(self):
        """Start a thread to monitor for global Escape key press."""
        def monitor_escape():
            while self.running:
                # Check if Escape key is pressed (VK_ESCAPE = 0x1B)
                if win32api.GetAsyncKeyState(0x1B) & 0x8000:
                    self.close_overlay()
                    break
                time.sleep(0.1)
        
        hotkey_thread = threading.Thread(target=monitor_escape, daemon=True)
        hotkey_thread.start()
        
    def find_client_window(self):
        """Find a window with 'client' in its title and return its geometry."""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if 'client' in window_title.lower():
                    rect = win32gui.GetWindowRect(hwnd)
                    windows.append({
                        'title': window_title,
                        'rect': rect,
                        'hwnd': hwnd
                    })
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            # Return the first client window found
            return windows[0]['rect']
        return None
        
    def setup_window(self):
        """Configure the transparent overlay window."""
        # Remove window decorations
        self.root.overrideredirect(True)
        
        # Make window transparent
        self.root.attributes('-alpha', 0.8)
        self.root.attributes('-topmost', True)
        
        # Try to find client window first
        client_rect = self.find_client_window()
        
        if client_rect:
            # Use client window dimensions and position
            x, y, right, bottom = client_rect
            width = right - x
            height = bottom - y
            self.root.geometry(f"{width}x{height}+{x}+{y}")
            print(f"Found client window, matching its geometry: {width}x{height}+{x}+{y}")
        else:
            # Fallback to center quarter of screen
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            quarter_width = screen_width // 2
            quarter_height = screen_height // 2
            x = screen_width // 4
            y = screen_height // 4
            self.root.geometry(f"{quarter_width}x{quarter_height}+{x}+{y}")
            print("No client window found, using center quarter of screen")
        
        # Make background transparent/invisible
        self.root.configure(bg='black')
        self.root.attributes('-transparentcolor', 'black')
        
    def create_content(self):
        """Create the status display text in the lower left."""
        self.status_label = tk.Label(
            self.root,
            text="Initializing...",
            fg="red",
            bg="black",
            font=("Arial", 8),
            justify="left"
        )
        self.status_label.place(relx=0.0, rely=0.80, anchor="sw")
        
    def bind_keys(self):
        """Bind keyboard shortcuts."""
        self.root.bind('<Escape>', self.close_overlay)
        self.root.focus_set()  # Ensure window can receive key events
        
    def start_status_updates(self):
        """Start the periodic status update loop."""
        self.update_status()

    def update_status(self):
        """Update the status display with current game state."""
        if not self.running:
            return

        try:
            if self.trigger_manager:
                # Get current state values
                auto_attack = self.trigger_manager.get_state("auto_attack", False)
                in_combat = self.trigger_manager.get_state("in_combat", False)
                meditating = self.trigger_manager.get_state("meditating", False)
                casting_spell = self.trigger_manager.get_state("casting_spell", False)
                is_dead = self.trigger_manager.get_state("is_dead", False)

                # Build status text
                status_lines = []

                # Auto attack status
                if auto_attack:
                    status_lines.append("⚔️ Auto Attack: ON")
                else:
                    status_lines.append("⚔️ Auto Attack: OFF")

                # Combat status
                if in_combat:
                    status_lines.append("🗡️ Combat: ACTIVE")
                elif meditating:
                    status_lines.append("🧘 Meditating")
                elif casting_spell:
                    status_lines.append("✨ Casting Spell")
                elif is_dead:
                    status_lines.append("💀 DEAD")
                else:
                    status_lines.append("🕊️ Peaceful")

                status_text = "\n".join(status_lines)

                # Update color based on state
                if is_dead:
                    color = "red"
                elif in_combat:
                    color = "orange"
                elif auto_attack:
                    color = "yellow"
                else:
                    color = "lightgreen"

            else:
                status_text = "No trigger manager connected"
                color = "gray"

            # Update the label
            if self.status_label:
                self.status_label.config(text=status_text, fg=color)

        except Exception as e:
            if self.status_label:
                self.status_label.config(text=f"Error: {e}", fg="red")

        # Schedule next update
        if self.running:
            self.root.after(self.update_interval, self.update_status)

    def close_overlay(self, event=None):
        """Close the overlay immediately."""
        self.running = False
        self.root.quit()
        self.root.destroy()
        
    def run(self):
        """Start the overlay."""
        print("Starting transparent overlay...")
        print("Press Ctrl+C in terminal to exit")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\nOverlay stopped.")
            self.root.destroy()

def main(trigger_manager: Optional[TriggerManager] = None):
    # Check if running on Windows
    if os.name != 'nt':
        print("This script is designed for Windows 11 only.")
        sys.exit(1)

    overlay = TransparentOverlay(trigger_manager)
    overlay.run()

if __name__ == "__main__":
    main()



