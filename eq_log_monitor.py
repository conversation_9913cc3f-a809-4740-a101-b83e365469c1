#!/usr/bin/env python3
"""
EverQuest Log Monitor
Monitors EverQuest log files in real-time and echoes new content to the terminal.
"""

import os
import sys
import time
import argparse
import getpass
from pathlib import Path
from typing import Optional


class EQLogMonitor:
    def __init__(self, log_file_path: str, poll_interval: float = 0.1):
        """
        Initialize the EQ log monitor.
        
        Args:
            log_file_path: Path to the EverQuest log file
            poll_interval: How often to check for new content (seconds)
        """
        self.log_file_path = Path(log_file_path)
        self.poll_interval = poll_interval
        self.last_position = 0
        
    def find_latest_log(self, eq_directory: str) -> Optional[Path]:
        """
        Find the most recently modified log file in the EQ Logs directory.

        Args:
            eq_directory: Path to EverQuest installation directory

        Returns:
            Path to the most recent log file, or None if not found
        """
        # Try multiple possible log locations
        possible_log_dirs = [
            Path(eq_directory) / "Logs",
            Path(eq_directory) / "logs",  # lowercase variant
            Path(eq_directory),  # logs might be in root directory
        ]

        # Also check Windows UAC virtualization paths if on Windows
        if os.name == 'nt':  # Windows
            username = getpass.getuser()
            virtualstore_path = Path(f"C:/Users/<USER>/AppData/Local/VirtualStore")

            # Add virtualized paths
            if "Program Files" in eq_directory:
                try:
                    eq_path = Path(eq_directory)
                    if eq_path.is_absolute() and eq_path.parts[0].endswith(':'):
                        # Remove drive letter for relative path
                        relative_parts = eq_path.parts[1:]
                        virt_path = virtualstore_path.joinpath(*relative_parts)
                        possible_log_dirs.extend([
                            virt_path / "Logs",
                            virt_path / "logs",
                            virt_path
                        ])
                except Exception:
                    pass  # Skip virtualization if path parsing fails

        log_files = []
        found_dirs = []

        for logs_dir in possible_log_dirs:
            if logs_dir.exists():
                found_dirs.append(logs_dir)
                # Look for EQ log files
                patterns = ["eqlog_*.txt", "*.log", "*.txt"]
                for pattern in patterns:
                    log_files.extend(list(logs_dir.glob(pattern)))

        if not found_dirs:
            print(f"No accessible directories found. Tried:")
            for dir_path in possible_log_dirs:
                print(f"  - {dir_path}")
            return None

        if not log_files:
            print(f"No log files found in accessible directories:")
            for dir_path in found_dirs:
                print(f"  - {dir_path}")
            print("\nMake sure logging is enabled in EverQuest with '/log on'")
            return None

        # Return the most recently modified log file
        latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
        return latest_log
    
    def validate_log_file(self) -> bool:
        """
        Validate that the log file exists and is readable.
        
        Returns:
            True if the log file is valid, False otherwise
        """
        if not self.log_file_path.exists():
            print(f"Log file not found: {self.log_file_path}")
            return False
            
        if not self.log_file_path.is_file():
            print(f"Path is not a file: {self.log_file_path}")
            return False
            
        try:
            with open(self.log_file_path, 'r', encoding='utf-8', errors='ignore'):
                pass
        except PermissionError:
            print(f"Permission denied reading file: {self.log_file_path}")
            return False
        except Exception as e:
            print(f"Error accessing file: {self.log_file_path} - {e}")
            return False
            
        return True
    
    def get_file_size(self) -> int:
        """Get the current size of the log file."""
        try:
            return self.log_file_path.stat().st_size
        except (OSError, FileNotFoundError):
            return 0
    
    def read_new_content(self) -> str:
        """
        Read new content from the log file since the last read.
        
        Returns:
            New content as a string
        """
        try:
            current_size = self.get_file_size()
            
            # If file was truncated or is smaller, reset position
            if current_size < self.last_position:
                self.last_position = 0
            
            # If no new content, return empty string
            if current_size <= self.last_position:
                return ""
            
            with open(self.log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(self.last_position)
                new_content = f.read()
                self.last_position = f.tell()
                
            return new_content
            
        except Exception as e:
            print(f"Error reading log file: {e}")
            return ""
    
    def monitor(self):
        """
        Start monitoring the log file and echo new content to terminal.
        """
        if not self.validate_log_file():
            return
        
        print(f"Monitoring EverQuest log file: {self.log_file_path}")
        print(f"Poll interval: {self.poll_interval} seconds")
        print("Press Ctrl+C to stop monitoring")
        print("-" * 50)
        
        # Set initial position to end of file to only show new content
        self.last_position = self.get_file_size()
        
        try:
            while True:
                new_content = self.read_new_content()
                if new_content:
                    # Print new content without extra newlines
                    print(new_content, end='', flush=True)
                
                time.sleep(self.poll_interval)
                
        except KeyboardInterrupt:
            print("\nMonitoring stopped.")
        except Exception as e:
            print(f"\nError during monitoring: {e}")


def suggest_common_paths():
    """Suggest common EverQuest installation paths."""
    common_paths = [
        "C:\\TAKPv22",
        "C:\\TAKP",
        "C:\\Program Files\\Sony\\EverQuest",
        "C:\\Program Files (x86)\\Sony\\EverQuest",
        "C:\\Program Files\\Daybreak Game Company\\Installed Games\\EverQuest",
        "C:\\Program Files (x86)\\Daybreak Game Company\\Installed Games\\EverQuest",
        "C:\\EverQuest",
        "C:\\Games\\EverQuest"
    ]

    print("Common EverQuest installation paths to try:")
    for path in common_paths:
        if Path(path).exists():
            print(f"  ✓ {path} (exists)")
        else:
            print(f"  ✗ {path}")
    print()


def main():
    parser = argparse.ArgumentParser(
        description="Monitor EverQuest log files in real-time",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Monitor a specific log file
  python eq_log_monitor.py -f "C:\\TAKPv22\\Logs\\eqlog_Playername_server.txt"

  # Auto-find the latest log file in EQ directory
  python eq_log_monitor.py -d "C:\\TAKPv22"

  # Monitor with custom poll interval
  python eq_log_monitor.py -d "C:\\TAKPv22" -i 0.5

  # Show common installation paths
  python eq_log_monitor.py --suggest-paths
        """
    )
    
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument(
        '-f', '--file',
        help='Path to specific EverQuest log file'
    )
    group.add_argument(
        '-d', '--directory',
        help='Path to EverQuest installation directory (will find latest log)'
    )
    
    parser.add_argument(
        '-i', '--interval',
        type=float,
        default=0.1,
        help='Poll interval in seconds (default: 0.1)'
    )

    parser.add_argument(
        '--suggest-paths',
        action='store_true',
        help='Show common EverQuest installation paths and exit'
    )

    args = parser.parse_args()

    if args.suggest_paths:
        suggest_common_paths()
        return
    
    if args.file:
        log_file = args.file
    else:
        monitor = EQLogMonitor("", args.interval)
        latest_log = monitor.find_latest_log(args.directory)
        if not latest_log:
            sys.exit(1)
        log_file = str(latest_log)
        print(f"Found latest log file: {latest_log}")
    
    monitor = EQLogMonitor(log_file, args.interval)
    monitor.monitor()


if __name__ == "__main__":
    main()
